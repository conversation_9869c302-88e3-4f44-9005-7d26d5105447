<template>
  <div class="project-app-wrapper">
    <div class="left">
      <div class="main-content uranus-scrollbar dark" style="position: relative;">
        <CreatePlan />
      </div>
      <div style="position: absolute;top: 40px;left: 100%;z-index: 2;">
        <TaskPanel v-if="taskRoute" :item-size="itemSize" @showPlanList="closeList" @AmpJobId="AmpJobId"
          @showAmp="showAmp" @AmpJobVideo="AmpJobVideo" @AmpJobMedia="AmpJobMedia" :zoom="8" />
      </div>
      <div style="position: absolute;top: 0;left: 100%;z-index: 2;">
        <el-button type="primary" class="btn" @click="isShow">
          任务列表-{{ taskRoute ? '关闭' : '展示' }}
        </el-button>
      </div>
    </div>
    <div class="right">
    <div class="map-wrapper">
      <div class="g-map-wrapper">
        <div v-if="lineArr && lineArr.length > 0" class="leftManageTop">
          <el-row>
            <el-col :span="5">
              <el-button type="primary" class="btn" @click="startAnimation">开始动画</el-button>
            </el-col>
            <el-col :span="5">
              <el-button type="primary" class="btn" @click="pauseAnimation">暂停动画</el-button>
            </el-col>
            <el-col :span="5">
              <el-button type="primary" class="btn" @click="resumeAnimation">继续动画</el-button>
            </el-col>
            <el-col :span="4">
              <!--<el-button type="primary" class="btn" @click="stopAnimation">停止动画</el-button>-->
              <el-select v-model="speed" style="width: 100px;top: 5px;" placeholder="选择倍速"
                @change="handleSelect($event)">
                <el-option :value="'1倍速'">1倍速</el-option>
                <el-option :value="'2倍速'">2倍速</el-option>
                <el-option :value="'3倍速'">3倍速</el-option>
                <el-option :value="'4倍速'">4倍速</el-option>
              </el-select>
            </el-col>
            <el-col :span="5">
              <el-button type="primary" class="btn" @click="toggleFollow">
                {{ isFollowing ? '取消跟随' : '视角跟随' }}
              </el-button>
            </el-col>
            <!-- <el-col :span="4" v-if="getVideoShow">
              <el-button type="primary" class="btn" @click="getVideo">轨迹视频</el-button>
            </el-col> -->
          </el-row>
        </div>
        <div v-if="lineArr && lineArr.length > 0" class="leftManageLeft">
          <div style="margin: 20px;">
            <p>
              <span>经度：</span><span>{{ longitude }}</span>
            </p>
            <p>
              <span>纬度：</span><span>{{ latitude }}</span>
            </p>
            <p>
              <span>高度：</span><span>{{ height }}</span>
            </p>
            <!-- <p>
              <span>相对起飞点高度：</span><span>{{ elevation }}</span>
            </p>
            <p>
              <span>水平速度：</span><span>{{ horizontalSpeed }}</span>
            </p>
            <p>
              <span>距离Home点的距离：</span><span>{{ homeDistance }}</span>
            </p> -->
          </div>
        </div>
        <div v-if="timeData && timeData.distance" class="leftManage">
          <div class="info-container" v-if="!timeData.PolygonArea">
            <div class="info-item">
              <div class="info-title">航线长度</div>
              <div class="info-value">{{ timeData && timeData.distance ? timeData.distance + 'm' : '' }}</div>
            </div>
            <div class="info-item">
              <div class="info-title">预计执行时间</div>
              <div class="info-value">{{ timeData && timeData.workTime ? timeData.workTime : '' }}</div>
            </div>
            <div class="info-item">
              <div class="info-title">航点</div>
              <div class="info-value">{{ timeData && timeData.pointCount ? timeData.pointCount : '' }}</div>
            </div>
          </div>
          <div class="info-container" v-if="timeData.PolygonArea">
            <div class="info-item">
              <div class="info-title">面积</div>
              <div class="info-value">{{ timeData?.PolygonArea?.toFixed(2) + '㎡' }}</div>
            </div>
            <div class="info-item">
              <div class="info-title">航线长度</div>
              <div class="info-value">{{ timeData?.distance?.toFixed(1) + 'm' }}</div>
            </div>
            <div class="info-item">
              <div class="info-title">预计总时长</div>
              <div class="info-value">{{ formatSeconds(timeData?.time?.toFixed(0)) || 0 }}</div>
            </div>
            <div class="info-item">
              <div class="info-title">预计总照片数</div>
              <div class="info-value">{{ timeData?.pallcont || 0 }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>
  <!-- 背景遮罩 -->
  <div v-if="visible" class="modal-overlay" @click="closeModal"></div>
  <!-- 模拟模态框的 div -->
  <div v-if="visible" class="custom-modal">
    <videoAmp :VideoId="jobId" @closeExit="closeExit"></videoAmp>
  </div>
  <el-dialog v-model="mediaListVisible" title="文件列表" width="50%">
    <el-table :data="mediaFiles">
      <el-table-column property="name" label="文件名" />
      <el-table-column property="suffix" label="文件类型" />
      <el-table-column property="size" label="大小(bytes)" />
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button v-if="row.original_url" link type="primary" size="small"
            @click="downloadFile(row.original_url, row.name)">下载</el-button>
          <el-button v-if="row.preview_url" link type="primary" size="small" @click="handlePreview(row)">预览</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
  <el-image-viewer v-if="showImageViewer" :url-list="imageViewerList" :initial-index="imageViewerIndex"
    @close="showImageViewer = false" />
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, provide } from 'vue';
import TaskPanel from '@/components/task/TaskPanel.vue';
import CreatePlan from '@/components/task/CreatePlan.vue';
import { getWayLineDetail, getHistoryTrajectory, getCheckFlightMp4 } from '@/api/wayline';
import { getRoot } from '@/root';
import M30 from '@/assets/m30.png';
import videoAmp from '@/views/task/videoAmp.vue';
import store from '@/store';

let taskRoute = ref(true);
store.commit('MAP_TYPE', false);
store.commit('LAYER_MAP_TYPE', false);

const isShow = () => {
  taskRoute.value = !taskRoute.value
}

const itemSize = ref({ width: 1000, height: 800 });

const handleResize = () => {
  const viewportWidth = window.innerWidth;
  const baseWidth = 130; // 假设基础宽度为800px
  const scale = viewportWidth / baseWidth;

  itemSize.value.width = 100 * scale;
  itemSize.value.height = 100 * scale;
};

provide('itemSize', itemSize);

onMounted(() => {
  handleResize(); // 初始化时调用一次
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  if (marker.value) {
    marker.value.setMap(null); // 移除当前标记
    longitude.value = '';
    latitude.value = '';
    height.value = '';
    elevation.value = '';
    horizontalSpeed.value = '';
    homeDistance.value = '';
  }
  if (polyline.value) {
    polyline.value.setMap(null); // 移除当前路径
  }
  if (passedPolyline.value) {
    passedPolyline.value.setMap(null); // 移除已通过的路径
  }
  marker.value = null; // 将marker声明为全局变量
  polyline.value = null; // 将polyline声明为全局变量
  passedPolyline.value = null; // 将passedPolyline声明为全局变量
  lineArr.value = []; // 初始化路径数组
  uavDetails.value = [];
  timeData.value = {};

  // 清理Cesium相关资源
  if (viewer.value) {
    // 移除事件监听器
    viewer.value.scene.preRender.removeEventListener(updateUAVDetails);

    // 停止动画
    if (clock.value) {
      clock.value.shouldAnimate = false;
    }

    // 取消实体跟随
    viewer.value.trackedEntity = undefined;

    // 移除实体
    if (animationEntity.value) {
      viewer.value.entities.remove(animationEntity.value);
      animationEntity.value = null;
    }

    if (visionCone.value) {
      viewer.value.entities.remove(visionCone.value);
      visionCone.value = null;
    }

    if (directionLineEntity.value) {
      viewer.value.entities.remove(directionLineEntity.value);
      directionLineEntity.value = null;
    }

    if (flightPath.value) {
      viewer.value.entities.remove(flightPath.value);
      flightPath.value = null;
    }

    // 清理其他Cesium相关变量
    positionProperty.value = null;
  }

  window.removeEventListener('resize', handleResize);
});

const closeList = (value) => {
  taskRoute.value = value
}

let timeData = ref({})
/* 不能在TaskPanel组件中写函数方法,影响到生命周期 */
const showAmp = (id) => {
  if (id === '一键起飞') {
    timeData.value = {}
    return
  }
  // getWayLineDetail(id).then(res => {
  //   if (res.code !== 0) {
  //     return;
  //   } else {
  //     // 飞行航线数据统计
  //     if (res.data.waylineData.template_type !== 'waypoint') {
  //       timeData.value = res.data['3ddata'];
  //     } else {
  //       timeData.value = res.data.wayLineCount;
  //     }
  //   }
  // })
}

const formatSeconds = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}h ${minutes}m ${remainingSeconds}s`;
  } else {
    return `${minutes}m ${remainingSeconds}s`;
  }
}

let marker = ref(null); // 将marker声明为全局变量
let polyline = ref(null); // 将polyline声明为全局变量
let passedPolyline = ref(null); // 将passedPolyline声明为全局变量
let lineArr = ref([]); // 初始化路径数组
let uavDetails = ref([]); // 初始化路径数组详情数据
const viewer = ref(null);
const clock = ref(null); // 添加时钟引用
const animationEntity = ref(null); // 添加无人机实体引用
const positionProperty = ref(null); // 添加位置属性引用
const isFollowing = ref(true); // 是否开启视角跟随
const visionCone = ref(null); // 添加视锥体引用
const cameraFovEntity = ref(null); // 添加相机视场实体引用
const directionLineEntity = ref(null); // 添加方向线实体引用
const flightPathBoundingSphere = ref(null); // 飞行路径的边界球

//接收参数清除历史轨迹
const AmpJobId = async (id) => {
  speed.value = '1倍速';
  duration.value = 500;
  // 停止并清除上一个轨迹动画
  if (marker.value) {
    marker.value.stopMove(); // 停止当前标记的移动
    marker.value.setMap(null); // 移除当前标记
    longitude.value = '';
    latitude.value = '';
    height.value = '';
    elevation.value = '';
    horizontalSpeed.value = '';
    homeDistance.value = '';
  }
  if (polyline.value) {
    polyline.value.setMap(null); // 移除当前路径
  }
  if (passedPolyline.value) {
    passedPolyline.value.setMap(null); // 移除已通过的路径
  }

  // 清理Cesium相关资源
  if (viewer.value) {
    // 移除事件监听器
    viewer.value.scene.preRender.removeEventListener(updateUAVDetails);

    // 停止动画
    if (clock.value) {
      clock.value.shouldAnimate = false;
    }

    // 取消实体跟随
    viewer.value.trackedEntity = undefined;

    // 移除实体
    if (animationEntity.value) {
      viewer.value.entities.remove(animationEntity.value);
      animationEntity.value = null;
    }

    if (visionCone.value) {
      viewer.value.entities.remove(visionCone.value);
      visionCone.value = null;
    }

    if (directionLineEntity.value) {
      viewer.value.entities.remove(directionLineEntity.value);
      directionLineEntity.value = null;
    }

    if (flightPath.value) {
      viewer.value.entities.remove(flightPath.value);
      flightPath.value = null;
    }

    // 清理其他Cesium相关变量
    positionProperty.value = null;
  }

  lineArr.value = []; // 清空之前的路径数组
  uavDetails.value = [];
  await getHistoryTrajectory({ taskUuid: id }).then(res => {
    if (res.data.data != '') {
      const data = res.data.data
      data.track.points.forEach(location => {
        lineArr.value.push([location.longitude, location.latitude]);
        uavDetails.value.push({
          ...location,
          // 确保姿态角度数据存在，如果后端没有提供，使用默认值
          attitude_head: location.attitude_head !== undefined ? location.attitude_head : 0,
          attitude_pitch: location.attitude_pitch !== undefined ? location.attitude_pitch : 0,
          attitude_roll: location.attitude_roll !== undefined ? location.attitude_roll : 0
        });
      })
    } else {
      return;
    }
  })

  // 飞行航线回显地图
  viewer.value = getRoot().$viewer;
  if (viewer.value) {
    createCesiumEntities();
  }
}
const flightPath = ref(null)
function createCesiumEntities() {
  if (!viewer.value || lineArr.value.length === 0) return;

  // 创建时间线数据
  createTimelineData();

  // 创建航线路径
  flightPath.value = viewer.value.entities.add({
    name: 'Flight Path',
    polyline: {
      positions: Cesium.Cartesian3.fromDegreesArrayHeights(
        lineArr.value.flatMap((point, index) => {
          const height = uavDetails.value[index]?.height || 100;
          return [point[0], point[1], height];
        })
      ),
      width: 4,
      material: Cesium.Color.fromCssColorString('#0aed8b')
    }
  });

  // 计算整个飞行路径的边界范围
  const boundingSphere = Cesium.BoundingSphere.fromPoints(
    Cesium.Cartesian3.fromDegreesArrayHeights(
      lineArr.value.flatMap((point, index) => {
        const height = uavDetails.value[index]?.height || 100;
        return [point[0], point[1], height];
      })
    )
  );

  flightPathBoundingSphere.value = boundingSphere;

  // 使用flyToBoundingSphere方法自动计算最佳视角以查看整个路径
  viewer.value.camera.flyToBoundingSphere(boundingSphere, {
    offset: new Cesium.HeadingPitchRange(0, Cesium.Math.toRadians(-60), boundingSphere.radius * 3),
    duration: 1 // 快速过渡
  });

  // 创建无人机实体
  animationEntity.value = viewer.value.entities.add({
    name: 'UAV',
    position: positionProperty.value,
    orientation: new Cesium.VelocityOrientationProperty(positionProperty.value),
    billboard: {
      image: M30,
      width: 32,
      height: 32,
      scale: 1.0,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM
    },
    path: {
      resolution: 1,
      material: new Cesium.PolylineGlowMaterialProperty({
        glowPower: 0.2,
        color: Cesium.Color.GREEN
      }),
      width: 3
    }
  });

  // 创建视锥体 - 使用四棱锥表示视锥体
  visionCone.value = viewer.value.entities.add({
    name: 'Vision Cone',
    position: new Cesium.CallbackProperty((time) => {
      const dronePosition = positionProperty.value.getValue(time);
      if (!dronePosition) {
        return undefined;
      }

      const index = getCurrentPositionIndex(time);
      if (index < 0) return dronePosition;

      const detail = uavDetails.value[index];
      const heading = Cesium.Math.toRadians(detail.attitude_head !== undefined ? detail.attitude_head : 0);
      const pitch = Cesium.Math.toRadians((detail.attitude_pitch !== undefined ? detail.attitude_pitch : 0) - 90);
      const roll = Cesium.Math.toRadians(detail.attitude_roll !== undefined ? detail.attitude_roll : 0);

      const hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
      const orientation = Cesium.Transforms.headingPitchRollQuaternion(dronePosition, hpr);

      const rotationMatrix = Cesium.Matrix3.fromQuaternion(orientation, new Cesium.Matrix3());

      const direction = Cesium.Matrix3.getColumn(rotationMatrix, 2, new Cesium.Cartesian3());

      const cylinderLength = 30.0;
      const offset = Cesium.Cartesian3.multiplyByScalar(direction, cylinderLength / 2.0, new Cesium.Cartesian3());

      return Cesium.Cartesian3.subtract(dronePosition, offset, new Cesium.Cartesian3());
    }, false),
    orientation: new Cesium.CallbackProperty((time) => {
      const index = getCurrentPositionIndex(time);

      if (index >= 0 && index < uavDetails.value.length) {
        const detail = uavDetails.value[index];

        // 将无人机姿态角度转换为弧度
        // heading对应航向角，正北为0，顺时针增加
        const heading = Cesium.Math.toRadians(detail.attitude_head !== undefined ? detail.attitude_head : 0);
        // pitch对应俯仰角，水平为0，向上为正，向下为负
        // 我们减去90度以使锥体向下（相机通常朝向地面）
        const pitch = Cesium.Math.toRadians((detail.attitude_pitch !== undefined ? detail.attitude_pitch : 0) - 90);
        // roll对应横滚角
        const roll = Cesium.Math.toRadians(detail.attitude_roll !== undefined ? detail.attitude_roll : 0);

        const position = positionProperty.value.getValue(time);
        if (!position) return Cesium.Quaternion.IDENTITY;

        // 创建四元数表示视锥体的旋转
        return Cesium.Transforms.headingPitchRollQuaternion(
          position,
          new Cesium.HeadingPitchRoll(heading, pitch, roll)
        );
      }
      return Cesium.Quaternion.IDENTITY;
    }, false),
    // 使用四棱锥表示视锥体
    cylinder: {
      length: 30, // 视锥体长度
      topRadius: 0, // 顶部半径（尖端）
      bottomRadius: 15, // 底部半径
      material: Cesium.Color.MEDIUMAQUAMARINE.withAlpha(0.5),
      slices: 4, // 四棱锥
      outline: true,
      outlineColor: Cesium.Color.MEDIUMAQUAMARINE,
    }
  });

  if (isFollowing.value) {
    enableEntityFollow();
  }
}

function createTimelineData() {
  const start = Cesium.JulianDate.fromDate(new Date());
  const stop = Cesium.JulianDate.addSeconds(start, lineArr.value.length * (duration.value / 1000), new Cesium.JulianDate());

  // 设置时钟
  viewer.value.clock.startTime = start.clone();
  viewer.value.clock.stopTime = stop.clone();
  viewer.value.clock.currentTime = start.clone();
  viewer.value.clock.clockRange = Cesium.ClockRange.CLAMPED;
  viewer.value.clock.multiplier = 1;

  // 保存时钟引用
  clock.value = viewer.value.clock;

  // 创建位置属性
  positionProperty.value = new Cesium.SampledPositionProperty();

  // 添加每个位置的采样点
  for (let i = 0; i < lineArr.value.length; i++) {
    const time = Cesium.JulianDate.addSeconds(start, i * (duration.value / 1000), new Cesium.JulianDate());
    const position = Cesium.Cartesian3.fromDegrees(
      lineArr.value[i][0],
      lineArr.value[i][1],
      uavDetails.value[i]?.height || 100
    );
    positionProperty.value.addSample(time, position);
  }
}


const speed = ref('1倍速');
const duration = ref(500);  // 轨迹回放时间
//开始
const longitude = ref('');
const latitude = ref('');
const height = ref('');
const elevation = ref('');
const horizontalSpeed = ref('');
const homeDistance = ref('');

function startAnimation() {
  if (!viewer.value || !clock.value) return;

  // 重置时钟到开始位置
  clock.value.currentTime = clock.value.startTime.clone();

  // 设置时钟为运行状态
  clock.value.shouldAnimate = true;

  // 确保无人机实体可见
  if (animationEntity.value) {
    animationEntity.value.show = true;
  }

  // 移除之前的事件监听器（如果有）以避免重复添加
  viewer.value.scene.preRender.removeEventListener(updateUAVDetails);

  // 添加新的事件监听器
  viewer.value.scene.preRender.addEventListener(updateUAVDetails);

  // 启用视角跟随
  if (isFollowing.value && animationEntity.value) {
    enableEntityFollow();
  }
}

function pauseAnimation() {
  if (!clock.value) return;

  // 暂停时钟
  clock.value.shouldAnimate = false;
}

function resumeAnimation() {
  if (!clock.value) return;

  // 恢复时钟运行
  clock.value.shouldAnimate = true;
}

// 重置动画函数 - 完全重置动画状态
function resetAnimation() {
  if (!viewer.value || !clock.value) return;

  // 停止动画
  clock.value.shouldAnimate = false;

  // 重置时钟到开始位置
  clock.value.currentTime = clock.value.startTime.clone();

  // 确保无人机实体可见
  if (animationEntity.value) {
    animationEntity.value.show = true;
  }

  // 重置位置到第一个点
  if (positionProperty.value && lineArr.value.length > 0) {
    const position = Cesium.Cartesian3.fromDegrees(
      lineArr.value[0][0],
      lineArr.value[0][1],
      uavDetails.value[0]?.height || 100
    );

    // 手动更新位置信息
    longitude.value = lineArr.value[0][0].toFixed(6);
    latitude.value = lineArr.value[0][1].toFixed(6);
    height.value = uavDetails.value[0]?.height || '';
    elevation.value = uavDetails.value[0]?.elevation || '';
    horizontalSpeed.value = uavDetails.value[0]?.horizontal_speed || '';
    homeDistance.value = uavDetails.value[0]?.home_distance || '';
  }
}

// 更新无人机详情信息的函数
function updateUAVDetails() {
  if (!positionProperty.value || !viewer.value || !clock.value || uavDetails.value.length === 0) return;

  // 获取当前时间
  const currentTime = clock.value.currentTime;

  // 检查是否到达动画结束时间
  if (Cesium.JulianDate.greaterThanOrEquals(currentTime, clock.value.stopTime)) {
    // 停止动画时确保图标仍然可见
    if (animationEntity.value) {
      animationEntity.value.show = true;
    }
    return;
  }

  // 计算当前索引
  const index = getCurrentPositionIndex(currentTime);
  const position = positionProperty.value.getValue(currentTime);

  if (position && isFollowing.value) {
    const detail = uavDetails.value[index];
    if (detail) {
      const heading = Cesium.Math.toRadians(detail.attitude_head !== undefined ? detail.attitude_head : 0);
      const range = flightPathBoundingSphere.value ? flightPathBoundingSphere.value.radius * 2.5 : 1000;
      viewer.value.camera.lookAt(position, new Cesium.HeadingPitchRange(heading + Cesium.Math.toRadians(-90), Cesium.Math.toRadians(-45), range));
    }
  }

  if (index >= 0 && index < uavDetails.value.length) {
    // 获取当前位置
    if (position) {
      const cartographic = Cesium.Cartographic.fromCartesian(position);
      longitude.value = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
      latitude.value = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);

      // 更新详情信息
      height.value = uavDetails.value[index].height;
      elevation.value = uavDetails.value[index].elevation;
      horizontalSpeed.value = uavDetails.value[index].horizontal_speed;
      homeDistance.value = uavDetails.value[index].home_distance;
    }
  }
}

function handleSelect(e) {
  // 更新时钟的乘数来控制速度
  const speedMultiplier = parseInt(e.charAt(0));
  if (clock.value) {
    clock.value.multiplier = speedMultiplier;
  }
  // 更新持续时间以便于创建新的轨迹时使用
  duration.value = 500 / speedMultiplier;
}

const visible = ref(false);
const jobId = ref('');
const mediaListVisible = ref(false);
const mediaFiles = ref([]);
const showImageViewer = ref(false);
const imageViewerList = ref([]);
const imageViewerIndex = ref(0);

const AmpJobVideo = (id) => {
  jobId.value = '';
  getCheckFlightMp4({ taskUuid: id }).then(res => {
    if (res.data.code === 0) {
      jobId.value = id;
      visible.value = true;
    } else {
      visible.value = false;
    }
  })
}

const AmpJobMedia = (id) => {
  getCheckFlightMp4({ taskUuid: id }).then(res => {
    if (res.data.code === 200) {
      mediaFiles.value = res.data.data;
      mediaListVisible.value = true;
    } else {
      mediaFiles.value = [];
      mediaListVisible.value = false;
    }
  })
}
const closeExit = () => {
  visible.value = false;
}

const isImage = (suffix) => {
  if (!suffix) return false;
  const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg'];
  return imageExtensions.includes(suffix.toLowerCase());
};

const handlePreview = (row) => {
  if (isImage(row.suffix)) {
    const allImages = mediaFiles.value
      .filter(file => isImage(file.suffix) && file.preview_url)
      .map(file => file.preview_url);

    if (allImages.length > 0) {
      const initialIndex = allImages.findIndex(url => url === row.preview_url);
      imageViewerList.value = allImages;
      imageViewerIndex.value = initialIndex > -1 ? initialIndex : 0;
      showImageViewer.value = true;
    }
  } else {
    downloadFile(row.preview_url, `preview_${row.name}`);
  }
};

const downloadFile = (url, fileName) => {
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', fileName);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// 启用实体跟随
function enableEntityFollow() {
  if (!viewer.value || !animationEntity.value) return;

  // 设置相机跟随无人机实体
  // viewer.value.trackedEntity = animationEntity.value;

  // 也可以设置跟随视角的偏移和方向
  // viewer.value.scene.camera.lookAt(
  //   animationEntity.value.position.getValue(viewer.value.clock.currentTime),
  //   new Cesium.HeadingPitchRange(0, -Math.PI/4, 200) // 方位角、俯仰角、距离
  // );
}

// 禁用实体跟随
function disableEntityFollow() {
  if (!viewer.value) return;

  // 取消跟随
  // viewer.value.trackedEntity = undefined;
}

// 切换跟随状态
function toggleFollow() {
  isFollowing.value = !isFollowing.value;

  if (isFollowing.value) {
    enableEntityFollow();
  } else {
    disableEntityFollow();
  }
}

// 辅助函数 - 获取当前位置索引
function getCurrentPositionIndex(time) {
  if (!clock.value || !uavDetails.value.length) return -1;

  const totalDuration = Cesium.JulianDate.secondsDifference(clock.value.stopTime, clock.value.startTime);
  const elapsedDuration = Cesium.JulianDate.secondsDifference(time, clock.value.startTime);
  const progress = elapsedDuration / totalDuration;
  return Math.min(Math.floor(progress * uavDetails.value.length), uavDetails.value.length - 1);
}
</script>

<style lang="scss" scoped>
.route-icon {
  color: #fff;
  font-size: 16px;
}

.project-app-wrapper {
  height: 100vh;
  display: flex;
}

.btn {
  position: fixed;
  z-index: 1;
  width: 100px;
  margin-top: 6px;
  margin-left: 6px;
}

.left {
  position: relative;
  height: 100vh;
  width: 325px; /* 设置固定宽度，防止内容撑开 */
  flex-shrink: 0; /* 防止被压缩 */
}

.main-content {
  height: 100%;
  overflow-y: auto;
  padding-right: 10px;
}

.right {
  flex-grow: 1; /* 占据剩余空间 */
  position: relative;
  min-width: 0; /* 防止flex子项溢出 */

  .map-wrapper {
    width: 100%;
    height: 100%;
  }
}

.g-map-wrapper {
  height: 100%;
  width: 100%;

  .g-action-panel {
    position: absolute;
    top: 16px;
    right: 16px;

    .g-action-item {
      width: 28px;
      height: 28px;
      background: white;
      color: rgb(81, 81, 81);
      border-radius: 2px;
      line-height: 28px;
      text-align: center;
      margin-bottom: 2px;
    }

    .g-action-item:hover {
      border: 1px solid rgb(81, 81, 81);
      border-radius: 2px;
    }
  }

  &:deep(.ant-btn) {
    &::after {
      display: none;
    }
  }
}

.leftManageTop {
  position: fixed;
  top: 10%;
  left: 42%;
  color: black;
  width: 600px;
  height: 60px;
  z-index: 1;
}

.leftManageLeft {
  background: #ffffff;
  position: fixed;
  bottom: 5%;
  left: 21%;
  color: black;
  width: 250px;
  z-index: 1;
}

.leftManage {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translate(-20%, 0);
  color: white;
  padding: 10px;
  z-index: 1;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  .info-container {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
  }

  .info-item {
    padding: 0 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 100px;

    &:not(:last-child) {
      border-right: 1px solid rgba(255, 255, 255, 0.2);
    }
  }

  .info-title {
    font-size: 16px;
    white-space: nowrap;
    margin-bottom: 5px;
  }

  .info-value {
    font-size: 18px;
    text-align: center;
  }
}

:deep(.el-select__wrapper) {
  height: 35px;

  background-color: #409eff;
}

:deep(.el-select__placeholder) {
  color: #ffffff;
  font-size: 12px;
}

:deep(.el-select__caret) {
  color: #ffffff;
}

:deep(.ant-table-column-title) {
  color: #000000 !important;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.custom-modal {
  position: fixed;
  top: 5%;
  left: 50%;
  transform: translateX(-50%);
  width: 1300px;
  height: 90%;
  background: white;
  z-index: 3;
  border-radius: 5px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f0f0f0;
  border-bottom: 1px solid #ddd;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
}

.modal-close {
  cursor: pointer;
  font-size: 20px;
}

.modal-body {
  padding: 20px;
}
</style>